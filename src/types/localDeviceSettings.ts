import type {CalendarViewType} from './homeCalendar';
import type {ChallengeParticipant} from './models';
import type {TrackingDeviceTypes} from './trackingDeviceTypes';

export type LocalDeviceSettings = {
  calendarDayGoalType?: 'steps' | 'mileage' | undefined;
  challengeParticipantState?: undefined | Record<string, ChallengeParticipant>;
  hasSeenHealthSyncAllModal?: true | undefined;
  hasSeenNotificationModal?: true | undefined;
  hasSeenChallengeNotificationModal?: true | undefined;
  hasSeenStreakExplanation?: true | undefined;
  hasPromptedStoreRating?: true | undefined;
  trackingDeviceTypesEmailed?: TrackingDeviceTypes[] | undefined;
  initialCalendarViewType?: CalendarViewType | undefined;
  numberOfTimesOpened?: number;
};
