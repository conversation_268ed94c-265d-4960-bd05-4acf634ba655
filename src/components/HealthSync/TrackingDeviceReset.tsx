import {useCallback} from 'react';
import {ButtonOutlined, Grid, Text} from '@base-components';
import {DEV_FEATURE_FLAGS, DOMAIN_CONSTANTS} from '@constants';
import {useAppUserSafe, useSetTrackingDeviceType} from '@contexts';

type TrackingDeviceResetProps = Record<string, unknown>;

export const TrackingDeviceReset: React.FC<TrackingDeviceResetProps> = () => {
  const {email} = useAppUserSafe();
  const isShowReset =
    DOMAIN_CONSTANTS().SPECIAL_USERS.CLEAR_TRACKING_DEVICE_SETTINGS_USERS.includes(email) &&
    !DEV_FEATURE_FLAGS().isAppStoreScreenshotMode;
  const setTrackingDevice = useSetTrackingDeviceType();
  const reset = useCallback(() =>
    setTrackingDevice({trackingDeviceType: undefined, trackingApp: undefined}),
  [setTrackingDevice]);

  if (!isShowReset) return null;

  return (
    <Grid container justifyContent='center' pt={2}>
      <Text pb={1} style={{fontStyle: 'italic'}}>
        Viewable only as a test user...
      </Text>
      <ButtonOutlined icon='refresh' mb={2} onPress={reset}>
        Reset Tracking Device Settings
      </ButtonOutlined>
    </Grid>
  );
};
