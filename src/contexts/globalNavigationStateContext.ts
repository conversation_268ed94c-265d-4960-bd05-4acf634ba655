import {useCallback, useMemo, useState} from 'react';
import type {InviteCode, UUIDString, WeightSample} from '@types';
import {createContainer, createUseTrackedUpdateByKey} from '@utils';

type WellnessQuizState = {
  isWellnessQuizOpen?: true | undefined;
};

export type NotificationSettingsState = {
  isChallengeNotificationSettingsOpen?: true | undefined;
  isHighlightWellnessBlog?: true | undefined;
  isShowFrequency?: true | undefined;
  isStreakNotificationSettingsOpen?: true | undefined;
  isWellnessBlogSettingsOpen?: true | undefined;
  isWellnessQuizSettingsOpen?: true | undefined;
  isWorkoutNotificationSettingsOpen?: true | undefined;
};

type StreakAndGoalState = {
  isEditGoalChallengeOpen?: true | undefined;
  isEditGoalHomeOpen?: true | undefined;
  isStreakExplanationOpen?: true | undefined;
};

type WeightScreenState = {
  isEditWeightSampleOpen?: true | undefined;
  weightSample?: WeightSample | undefined;
};

type ChallengeScreenState = {
  isChallengeDetailsOpen?: true | undefined;
  isChallengeWelcomeModalOpen?: true | undefined;
  viewParticipantId?: UUIDString | undefined;
  viewTeamId?: UUIDString | undefined;
};

type EditChallengeScreenState = {
  challengeGroupId?: UUIDString | undefined;
  challengeLevel?: number | undefined;
  teamId?: UUIDString | undefined;
};

type HomeCalendarFilters = {
  homeCalendarFilterType?: 'distance' | 'steps' | 'goalProgress' | undefined;
};

type InviteCodeState = {
  inviteCode?: InviteCode | undefined;
  isInviteCodeModalOpen?: true | undefined;
};

export type TrackingDevicePopUpStateTypes =
  | 'start'
  | 'deviceTrackingType'
  | 'integrationSwitchConfirmation'
  | 'additionalExplanation'
  | 'trackingApp'
  | 'allSet'
  | 'closed';

type TrackingDevicePopUpState = {
  trackingDevicePopUpState?: TrackingDevicePopUpStateTypes | undefined;
};

type GlobalNavigationState = WellnessQuizState &
  NotificationSettingsState &
  StreakAndGoalState &
  TrackingDevicePopUpState &
  WeightScreenState &
  HomeCalendarFilters &
  ChallengeScreenState &
  EditChallengeScreenState &
  InviteCodeState;

const {Provider, useTrackedState, useUpdate} = createContainer(
  () => useState<GlobalNavigationState>({}),
  'GlobalNavigationStateContext',
);

export {Provider as GlobalNavigationStateProvider};

type TruthyValueKeysOnly<T> = {
  [K in keyof T]: T[K] extends undefined | true ? K : never;
}[keyof T];

const createUseBooleanState = <K extends string & TruthyValueKeysOnly<GlobalNavigationState>>(
  key: K,
) => {
  const useBooleanUpdate = createUseTrackedUpdateByKey(useUpdate, key);
  return (): Record<K, boolean> & {toggleValue: (isOverrideValue?: boolean) => void} => {
    const setState = useBooleanUpdate();
    const state = useTrackedState()[key];

    const toggleValue = useCallback(
      (isOverrideValue?: boolean) => {
        // @ts-ignore dynamically typed
        setState(prevBool => {
          if (isOverrideValue === undefined) {
            return prevBool ? undefined : true;
          }
          return isOverrideValue;
        });
      },
      [setState],
    );

    return useMemo(
      () =>
        ({
          [key]: state,
          toggleValue,
        }) as Record<K, boolean> & {toggleValue: () => void},
      [state, toggleValue],
    );
  };
};

export const useWellnessQuizState = createUseBooleanState('isWellnessQuizOpen');
export const useWellnessQuizSettingsState = createUseBooleanState('isWellnessQuizSettingsOpen');
export const useChallengeNotificationSettingsState = createUseBooleanState(
  'isChallengeNotificationSettingsOpen',
);
export const useNotificationSettingsIsShowFrequency = createUseBooleanState('isShowFrequency');

export const useWorkoutNotificationSettingsState = createUseBooleanState(
  'isWorkoutNotificationSettingsOpen',
);

export const useStreakNotificationSettingsState = createUseBooleanState(
  'isStreakNotificationSettingsOpen',
);

export const useWellnessBlogSettingsState = createUseBooleanState('isWellnessBlogSettingsOpen');

export const useHighlightWellnessBlogState = createUseBooleanState('isHighlightWellnessBlog');

export const useIsStreakExplanationOpenState = createUseBooleanState('isStreakExplanationOpen');

export const useIsEditGoalHomeOpenState = createUseBooleanState('isEditGoalHomeOpen');
export const useIsEditGoalChallengeOpenState = createUseBooleanState('isEditGoalChallengeOpen');

export const useIsEditWeightSampleOpen = createUseBooleanState('isEditWeightSampleOpen');

export const useIsChallengeWelcomeModalOpen = createUseBooleanState('isChallengeWelcomeModalOpen');

export const useIsInviteCodeModalOpen = createUseBooleanState('isInviteCodeModalOpen');

export const useInviteCode = () => useTrackedState().inviteCode;

export const useSetInviteCode = createUseTrackedUpdateByKey(useUpdate, 'inviteCode');

export const useEditWeightSample = () => useTrackedState().weightSample;
export const useSetEditWeightSample = createUseTrackedUpdateByKey(useUpdate, 'weightSample');

export const useSetNotificationSettings = (key: keyof NotificationSettingsState) => {
  const update = useUpdate();
  return useCallback(() => {
    update(prev => ({...prev, [key]: true}));
  }, [key, update]);
};

export const useTrackingDevicePopUpNavState = () =>
  useTrackedState().trackingDevicePopUpState ?? 'closed';

export const useSetTrackingDevicePopUpState = createUseTrackedUpdateByKey(
  useUpdate,
  'trackingDevicePopUpState',
);

export const useHomeCalendarFilterType = () => {
  const {homeCalendarFilterType} = useTrackedState();
  return homeCalendarFilterType ?? 'distance';
};

export const useSetHomeCalendarFilterType = createUseTrackedUpdateByKey(
  useUpdate,
  'homeCalendarFilterType',
);

export const useIsChallengeDetailsOpen = createUseBooleanState('isChallengeDetailsOpen');

export const useChallengesCreateChallengeParams = () => {
  const {challengeGroupId, challengeLevel, teamId} = useTrackedState();
  return {challengeLevel, challengeGroupId, teamId} as const;
};

export const useSetChallengeGroupId = createUseTrackedUpdateByKey(useUpdate, 'challengeGroupId');
export const useSetChallengeLevel = createUseTrackedUpdateByKey(useUpdate, 'challengeLevel');
export const useSetChallengeTeamId = createUseTrackedUpdateByKey(useUpdate, 'teamId');
