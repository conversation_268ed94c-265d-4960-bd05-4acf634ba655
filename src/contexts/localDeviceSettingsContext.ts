import {useMutation} from '@tanstack/react-query';
import {useAtom, useAtomValue} from 'jotai';
import {useCallback, useEffect, useState} from 'react';
import {firebaseApi} from '@api';
import {CONTENT_CODES, DOMAIN_CONSTANTS, isAndroid, isIos, STORAGE_KEYS} from '@constants';
import {atomWithPersistentStorage, useBooleanDelay, useOnAppStateForeground} from '@hooks';
import {useLinkTo} from '@navigation';
import {
  type Challenge,
  type ChallengeParticipant,
  ChallengeType,
  isPrimaryTrackingDeviceType,
  type LocalDeviceSettings,
  StatusCodes,
  TrackingDeviceTypes,
  type UUIDString,
} from '@types';
import {
  createMergeUpdatesAndRemoveUndefinedAndEmptyValuesCallback,
  createUseSetterFromAtom,
  isDeepEqual,
  isNonEmptyArray,
  LOGGER,
  mergeUpdatesAndRemoveUndefinedValues,
  metersToMiles,
  roundStepCount,
  trackingDeviceLog,
} from '@utils';
import {useTrackingDeviceType} from './authContext';
import {
  type TrackingDevicePopUpStateTypes,
  useSetTrackingDevicePopUpState,
  useTrackingDevicePopUpNavState,
} from './globalNavigationStateContext';
import {useIsCurrentRouteSpecifiedRoute} from './useNavContainerConfig';

const DEFAULT_LOCAL_DEVICE_SETTINGS: LocalDeviceSettings = {};

const localDeviceSettingsAtom = atomWithPersistentStorage<LocalDeviceSettings>(
  STORAGE_KEYS.LOCAL_DEVICE_SETTINGS,
  DEFAULT_LOCAL_DEVICE_SETTINGS,
);

export const useLocalDeviceSettings = () => useAtomValue(localDeviceSettingsAtom);

const useNumberOfTimesOpened = () => useAtomValue(localDeviceSettingsAtom).numberOfTimesOpened ?? 0;

const useHasSeenNotificationModal = () =>
  !!useAtomValue(localDeviceSettingsAtom).hasSeenNotificationModal;

const useHasSeenStreakExplanation = () =>
  !!useAtomValue(localDeviceSettingsAtom).hasSeenStreakExplanation;
export const useHasSeenHealthSyncAllModal = () =>
  !!useAtomValue(localDeviceSettingsAtom).hasSeenHealthSyncAllModal;

export const useHasSeenChallengeNotificationModal = () =>
  !!useAtomValue(localDeviceSettingsAtom).hasSeenChallengeNotificationModal;

const useChallengeParticipantState = (challengeId: UUIDString) =>
  useAtomValue(localDeviceSettingsAtom).challengeParticipantState?.[challengeId];

const mergePropertiesCallback =
  createMergeUpdatesAndRemoveUndefinedAndEmptyValuesCallback<LocalDeviceSettings>(
    mergeUpdatesAndRemoveUndefinedValues,
  );

const useSetLocalDeviceSettings = createUseSetterFromAtom(
  localDeviceSettingsAtom,
  mergePropertiesCallback,
);

export const useSetHasPromptedStoreRating = () =>
  useSetLocalDeviceSettings(prev =>
    prev.hasPromptedStoreRating ? prev : {...prev, hasPromptedStoreRating: true},
  );

export const useSetHasSeenNotificationModal = () =>
  useSetLocalDeviceSettings(prev =>
    prev.hasSeenNotificationModal ? prev : {...prev, hasSeenNotificationModal: true},
  );

const useSetHasSeenStreakExplanation = () =>
  useSetLocalDeviceSettings(prev =>
    prev.hasSeenStreakExplanation ? prev : {...prev, hasSeenStreakExplanation: true},
  );

export const useSetHasSeenHealthSyncAllModal = () =>
  useSetLocalDeviceSettings(prev =>
    prev.hasSeenHealthSyncAllModal ? prev : {...prev, hasSeenHealthSyncAllModal: true},
  );

export const useSetHasSeenChallengeNotificationModal = () =>
  useSetLocalDeviceSettings(prev =>
    prev.hasSeenChallengeNotificationModal
      ? prev
      : {...prev, hasSeenChallengeNotificationModal: true},
  );

const useSetChallengeParticipantState = () =>
  useSetLocalDeviceSettings(
    (prev, challengeId: UUIDString, participant: ChallengeParticipant | undefined) => {
      const isParticipantsEqual = isDeepEqual(
        prev.challengeParticipantState?.[challengeId],
        participant,
      );
      if (!participant || isParticipantsEqual) return prev;
      return {
        ...prev,
        challengeParticipantState: {
          ...prev.challengeParticipantState,
          [challengeId]: participant,
        },
      };
    },
  );

const useIncrementNumberOfTimesOpened = () =>
  useSetLocalDeviceSettings(prev => ({
    ...prev,
    numberOfTimesOpened: (prev.numberOfTimesOpened ?? 0) + 1,
  }));

export const useStreakExplanationPopUpModalState = (hasStreak: boolean | undefined) => {
  const [isOpen, setIsOpen] = useState(false);
  const hasSeenStreakExplanation = useHasSeenStreakExplanation();

  const hasRanOnceInitial = hasSeenStreakExplanation || false;
  const [hasRanOnce, setHasRanOnce] = useState(hasRanOnceInitial);

  useEffect(() => {
    // Only open the modal when
    // 1. The modal has not been seen
    // 2. The modal isn't currently open
    // 3. The user has a streak
    if (hasRanOnce || isOpen || !hasStreak) return;
    setIsOpen(true);
  }, [hasRanOnce, hasStreak, isOpen]);

  const setHasSeenStreakExplanation = useSetHasSeenStreakExplanation();
  const onDismiss = () => {
    setIsOpen(false);
    setHasRanOnce(true);
    setHasSeenStreakExplanation();
  };

  return {isOpen, onDismiss};
};

const getChallengeParticipantValue = (
  challenge: Challenge,
  participant: ChallengeParticipant | undefined,
) => {
  if (challenge.type === ChallengeType.DISTANCE) {
    return participant?.lastLoggedDistanceMeters;
  }

  if (challenge.type === ChallengeType.STEP) {
    return participant?.lastLoggedSteps;
  }
  LOGGER.warn('[Challenge] Not supported challenge type');
};

const getIsPastNextThreshold = (
  type: ChallengeType,
  previous: ChallengeParticipant | undefined,
  current: ChallengeParticipant | undefined,
) => {
  if (type === ChallengeType.DISTANCE) {
    return (
      (current?.lastLoggedDistanceMeters ?? 0) - (previous?.lastLoggedDistanceMeters ?? 0) >=
      DOMAIN_CONSTANTS().CHALLENGE.POPUP_DISTANCE_THRESHOLD_IN_METERS
    );
  }

  if (type === ChallengeType.STEP) {
    return (
      (current?.lastLoggedSteps ?? 0) - (previous?.lastLoggedSteps ?? 0) >=
      DOMAIN_CONSTANTS().CHALLENGE.POPUP_STEP_THRESHOLD
    );
  }

  return false;
};

const roundDownToMultipleOfDistanceThreshold = (value: number) =>
  Math.floor(value / DOMAIN_CONSTANTS().CHALLENGE.POPUP_DISTANCE_THRESHOLD_IN_METERS) *
  DOMAIN_CONSTANTS().CHALLENGE.POPUP_DISTANCE_THRESHOLD_IN_METERS;

const roundDownToMultipleOfStepThreshold = (value: number) =>
  Math.floor(value / DOMAIN_CONSTANTS().CHALLENGE.POPUP_STEP_THRESHOLD) *
  DOMAIN_CONSTANTS().CHALLENGE.POPUP_STEP_THRESHOLD;

const getChallengeValueRounded = (
  type: ChallengeType,
  participant: ChallengeParticipant | undefined,
) => {
  if (type === ChallengeType.DISTANCE && participant?.lastLoggedDistanceMeters) {
    return roundDownToMultipleOfDistanceThreshold(participant.lastLoggedDistanceMeters);
  }

  if (type === ChallengeType.STEP && participant?.lastLoggedSteps) {
    return roundDownToMultipleOfStepThreshold(participant.lastLoggedSteps);
  }

  return 0;
};

export const getDynamicContent = (
  challenge: Challenge,
  participant: ChallengeParticipant | undefined,
) => {
  const challengeValue = getChallengeValueRounded(challenge.type, participant);
  if (challenge.type === ChallengeType.DISTANCE) {
    return CONTENT_CODES().CHALLENGE.PROGRESS_MODAL.BODY_DISTANCE(
      metersToMiles(challengeValue).toFixed(0),
      challenge.challengeName,
    );
  }
  if (challenge.type === ChallengeType.STEP) {
    return CONTENT_CODES().CHALLENGE.PROGRESS_MODAL.BODY_STEPS(
      roundStepCount(challengeValue).toString(),
      challenge.challengeName,
    );
  }
  return '';
};

export const useChallengeMilestonePopUpModalState = (
  challenge: Challenge,
  participant: ChallengeParticipant | undefined,
) => {
  const [isOpen, setIsOpen] = useState(false);
  const participantState = useChallengeParticipantState(challenge.id);
  const setParticipantState = useSetChallengeParticipantState();
  const lastValue = getChallengeParticipantValue(challenge, participantState);
  const currentValue = getChallengeParticipantValue(challenge, participant);
  const areValuesTheSame = lastValue === currentValue;

  const isPastNextThreshold = getIsPastNextThreshold(challenge.type, participantState, participant);

  useEffect(() => {
    // Only open the modal when
    // 1. The modal isn't currently open
    // 2. The lastValue and the currentValue numbers are different
    // 3. The new threshold has been met
    if (isOpen || areValuesTheSame || !isPastNextThreshold) return;
    setIsOpen(true);
  }, [areValuesTheSame, isOpen, isPastNextThreshold]);

  const onDismiss = () => {
    setIsOpen(false);
    setParticipantState(challenge.id, participant);
  };

  return {isOpen, onDismiss};
};

const useAppForegroundEffect = () => {
  const incrementNumberOfTimesOpened = useIncrementNumberOfTimesOpened();

  useOnAppStateForeground(incrementNumberOfTimesOpened, undefined, 'NotificationPopupModal');
};

export const useNotificationPopUpModalState = () => {
  const [isOpen, setIsOpen] = useState(false);
  const hasSeenNotificationModal = useHasSeenNotificationModal();
  const numberOfTimesOpened = useNumberOfTimesOpened();
  const {hasBeenDelayed, resetDelayTimer, startDelayTimer} = useBooleanDelay(
    DOMAIN_CONSTANTS().NOTIFICATIONS.DELAY_BEFORE_NOTIFICATION_MODAL_MS,
  );
  const isCurrentScreenHomeScreen = useIsCurrentRouteSpecifiedRoute('home-screen');

  useAppForegroundEffect();

  useEffect(() => {
    // Only show the modal if on home screen
    if (!isCurrentScreenHomeScreen) {
      resetDelayTimer(); // reset timer if ever not on home screen

      return;
    }

    // Check if the app has been opened more than the threshold
    const hasBeenOpenedMoreThanThreshold =
      numberOfTimesOpened >= DOMAIN_CONSTANTS().NOTIFICATIONS.APP_OPENS_BEFORE_NOTIFICATION_MODAL;
    if (hasBeenOpenedMoreThanThreshold) {
      startDelayTimer();
    }
    // If not threshold, or the delay is loading, or the user has already seen modal, exit early
    if (!hasBeenOpenedMoreThanThreshold || !hasBeenDelayed || hasSeenNotificationModal) {
      return;
    }

    // Show the modal when
    // 1. The user is on the home screen
    // 2. The delay has passed
    // 3. The user has not seen the modal
    setIsOpen(true);
  }, [
    hasBeenDelayed,
    hasSeenNotificationModal,
    isCurrentScreenHomeScreen,
    numberOfTimesOpened,
    resetDelayTimer,
    startDelayTimer,
  ]);

  const toggleHasSeenNotificationModal = useSetHasSeenNotificationModal();
  const to = useLinkTo();

  const onDismiss = () => {
    setIsOpen(false);
    toggleHasSeenNotificationModal();
  };

  const onConfirm = () => {
    setIsOpen(false);
    toggleHasSeenNotificationModal();
    to.notificationSettingsScreen();
  };

  return {isOpen, onDismiss, onConfirm};
};

const allDataOriginsAtom = atomWithPersistentStorage<string[]>(
  STORAGE_KEYS.ANDROID_DATA_ORIGINS,
  [],
  {
    intervalMs: 1000,
    maxRetries: 5,
    hasChanged: (prev, next) => !isDeepEqual(prev, next),
  },
);

const useAllDataOrigins = () => useAtom(allDataOriginsAtom);

const selectedDataOriginAtom = atomWithPersistentStorage<string[]>(
  STORAGE_KEYS.ANDROID_SELECTED_DATA_ORIGIN,
  [],
);

const useSelectedDataOrigin = () => useAtom(selectedDataOriginAtom);

export const useDataSourcePopUpState = (onConfirm: () => Promise<void>) => {
  const [selectedOrigin = [], setSelectedOrigin] = useSelectedDataOrigin();
  const [allOrigins = []] = useAllDataOrigins();
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    // Only open the modal when
    // 1. The modal isn't currently open
    // 2. Is on android
    // 3. There are multiple origins
    // 4. An origin has not yet been selected
    const hasMultipleOrigins = allOrigins.length > 1;
    const hasSelectedOrigin = isNonEmptyArray(selectedOrigin);
    if (isOpen || !isAndroid || !hasMultipleOrigins || hasSelectedOrigin) return;
    setIsOpen(true);
  }, [allOrigins, isOpen, selectedOrigin]);

  const toggleOpen = useCallback(() => {
    setIsOpen(isOpenPrev => !isOpenPrev);
  }, [setIsOpen]);

  const onConfirmMutation = useMutation({
    mutationFn: onConfirm,
    mutationKey: ['onConfirmMutation'],
    onSettled: () => {
      setIsOpen(false);
    },
  });

  return {
    isOpen,
    toggleOpen,
    onConfirmMutation,
    allOrigins,
    selectedOrigin,
    setSelectedOrigin,
  };
};

export const useEmailTrackingDeviceTypeConnection = () => {
  const {trackingDeviceTypesEmailed} = useLocalDeviceSettings();
  const setWearableDeviceBrandsEmailed = useSetLocalDeviceSettings(
    (prev, type: TrackingDeviceTypes) => {
      if (prev.trackingDeviceTypesEmailed?.includes(type)) return prev;
      return {
        ...prev,
        trackingDeviceTypesEmailed: [...(prev.trackingDeviceTypesEmailed ?? []), type],
      };
    },
  );

  return useMutation({
    mutationFn: async (type: TrackingDeviceTypes) => {
      // Skip sending email if already has been emailed
      if (trackingDeviceTypesEmailed?.includes(type)) {
        trackingDeviceLog(
          `Skipping sending tracking device type email for ${type} since already sent`,
        );
        return;
      }
      // Skip if not a tracking device type not supported
      if (!isPrimaryTrackingDeviceType(type)) {
        trackingDeviceLog(`Skipping sending tracking device type email for ${type}`);
        return;
      }
      const response = await firebaseApi.emailTrackingDeviceConnection(type);
      if (response.status !== StatusCodes.OK_200) {
        trackingDeviceLog(`Error encountered when sending ${type} email`);
        throw new Error(`Failed to send tracking device connection email for brand ${type}`);
      }
      // Mark brand as successfully emailed if successful
      trackingDeviceLog(`Sent tracking device connection email for ${type}`);
      setWearableDeviceBrandsEmailed(type);
    },
    mutationKey: ['useEmailWearableConnection'],
  });
};

const STATE_TRANSITION_MAP = {
  start: 'deviceTrackingType',
  deviceTrackingType: 'additionalExplanation',
  additionalExplanation: 'trackingApp',
  trackingApp: 'allSet',
  allSet: 'closed',
  closed: 'closed',
} as Record<TrackingDevicePopUpStateTypes, TrackingDevicePopUpStateTypes>;

const useTrackingDevicePopUpStateContinue = (setHasAnsweredQuestion: (value: boolean) => void) => {
  const setOpenState = useSetTrackingDevicePopUpState();
  const trackingDeviceType = useTrackingDeviceType();
  return useCallback(() => {
    setOpenState(prevState => {
      setHasAnsweredQuestion(prevState !== 'start');
      const isStillNotSure =
        isIos &&
        trackingDeviceType === TrackingDeviceTypes.NoDevice &&
        prevState === 'additionalExplanation';
      const result = isStillNotSure
        ? 'deviceTrackingType'
        : STATE_TRANSITION_MAP[prevState ?? 'closed'];
      trackingDeviceLog(`Transitioning from ${prevState} to ${result}`);
      return result;
    });
  }, [setHasAnsweredQuestion, setOpenState, trackingDeviceType]);
};

export const useTrackingDevicePopUpState = () => {
  const deviceTrackingType = useTrackingDeviceType();
  const hasAttemptedSync = useHasSeenHealthSyncAllModal();
  const trackingDevicePopUpState = useTrackingDevicePopUpNavState();
  const setOpenState = useSetTrackingDevicePopUpState();

  // Use local state instead of local device settings - preload from user profile
  const [hasAnsweredQuestion, setHasAnsweredQuestion] = useState(() =>
    // If user already has a tracking device type set, they've answered the question
    deviceTrackingType !== undefined,
  );

  const onContinue = useTrackingDevicePopUpStateContinue(setHasAnsweredQuestion);

  // Update hasAnsweredQuestion when deviceTrackingType changes
  useEffect(() => {
    if (deviceTrackingType !== undefined) {
      setHasAnsweredQuestion(true);
    }
  }, [deviceTrackingType]);

  useEffect(() => {
    if (!hasAnsweredQuestion && hasAttemptedSync) {
      trackingDeviceLog('Opening up tracking device modal because not yet answered question');
      setOpenState('start');
    }
  }, [hasAnsweredQuestion, hasAttemptedSync, setOpenState]);

  const to = useLinkTo();
  useEffect(() => {
    if (
      trackingDevicePopUpState === 'allSet' &&
      deviceTrackingType === TrackingDeviceTypes.Fitbit
    ) {
      setOpenState('closed');
      trackingDeviceLog('Redirecting to health sync settings screen for Fitbit device type');
      // Redirect to health sync setting screen if and only if Fitbit brand
      to.healthSyncSettings();
    }
  }, [setOpenState, to, deviceTrackingType, trackingDevicePopUpState]);

  const onDismiss = useCallback(() => {
    trackingDeviceLog('Dismissing tracking device modal');
    setOpenState('closed');
  }, [setOpenState]);

  // Can only dismiss after answering question
  const isDismissable =
    hasAnsweredQuestion &&
    deviceTrackingType !== undefined &&
    (deviceTrackingType !== TrackingDeviceTypes.NoDevice ||
      (trackingDevicePopUpState !== 'deviceTrackingType' &&
        trackingDevicePopUpState !== 'additionalExplanation'));

  return {
    isDismissable,
    trackingDevicePopUpState,
    onDismiss,
    onContinue,
  };
};
